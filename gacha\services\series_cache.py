"""
簡化的系列緩存服務

提供基本的系列列表緩存，不包含複雜的搜索和過濾功能。
"""

from __future__ import annotations

from typing import List, Optional

from database.postgresql.async_manager import get_redis_client
from gacha.repositories._base_repo import fetch_all
from utils.logger import logger

# Redis緩存配置
SERIES_CACHE_KEY = "gacha:all_series"
SERIES_CACHE_TTL = 3600  # 1小時


async def get_series_cache(pool_type: Optional[str] = None) -> List[str]:
    """
    獲取系列緩存（支持按卡池類型過濾）

    Args:
        pool_type: 卡池類型過濾，None表示獲取所有系列

    Returns:
        系列名稱列表
    """
    if pool_type is None:
        # 獲取所有系列，使用Redis緩存
        redis_client = get_redis_client()

        # 嘗試從Redis獲取
        if redis_client:
            try:
                import json

                cached_raw = await redis_client.execute_command(
                    "JSON.GET", SERIES_CACHE_KEY
                )
                cached_data = json.loads(cached_raw) if cached_raw else None
                if cached_data:
                    logger.debug("[SERIES_CACHE] 從Redis JSON獲取成功")
                    return cached_data.get("series_list", [])
            except Exception as e:
                logger.warning("[SERIES_CACHE] Redis獲取失敗: %s", e)

        # 從數據庫重建
        return await _fetch_all_series()
    else:
        # 按卡池類型過濾，先檢查Redis緩存
        pool_cache_key = f"{SERIES_CACHE_KEY}:pool:{pool_type}"
        redis_client = get_redis_client()

        if redis_client:
            try:
                import json

                cached_raw = await redis_client.execute_command(
                    "JSON.GET", pool_cache_key
                )
                cached_data = json.loads(cached_raw) if cached_raw else None
                if cached_data:
                    logger.debug(
                        "[SERIES_CACHE] 從Redis JSON獲取pool_type='%s'成功", pool_type
                    )
                    return cached_data.get("series_list", [])
            except Exception as e:
                logger.warning(
                    "[SERIES_CACHE] Redis獲取pool_type='%s'失敗: %s", pool_type, e
                )

        # 從數據庫獲取並緩存
        return await _fetch_series_by_pool_type(pool_type, cache_key=pool_cache_key)


async def _fetch_all_series() -> List[str]:
    """從數據庫獲取所有系列並緩存"""
    try:
        query = "SELECT DISTINCT series FROM gacha_master_cards ORDER BY series"
        results = await fetch_all(query)
        series_list = [result["series"] for result in results] if results else []

        # 緩存到Redis
        redis_client = get_redis_client()
        if redis_client:
            try:
                cache_data = {
                    "series_list": series_list,
                    "count": len(series_list),
                    "cached_at": int(__import__("time").time()),
                }

                # 使用 execute_command 方式，與項目其他地方保持一致
                import json

                await redis_client.execute_command(
                    "JSON.SET", SERIES_CACHE_KEY, "$", json.dumps(cache_data)
                )
                await redis_client.expire(SERIES_CACHE_KEY, SERIES_CACHE_TTL)
                logger.info("[SERIES_CACHE] 已緩存 %d 個系列到Redis", len(series_list))
            except Exception as e:
                logger.warning("[SERIES_CACHE] Redis緩存失敗: %s", e)

        return series_list

    except Exception as e:
        logger.error("[SERIES_CACHE] 獲取系列失敗: %s", e, exc_info=True)
        return []


async def _fetch_series_by_pool_type(
    pool_type: str, cache_key: Optional[str] = None
) -> List[str]:
    """按卡池類型獲取系列列表並緩存"""
    try:
        query = """
            SELECT DISTINCT series 
            FROM gacha_master_cards 
            WHERE pool_type = $1 
            ORDER BY series
        """
        results = await fetch_all(query, [pool_type])
        series_list = [result["series"] for result in results] if results else []

        # 緩存到Redis
        if cache_key:
            redis_client = get_redis_client()
            if redis_client:
                try:
                    cache_data = {
                        "pool_type": pool_type,
                        "series_list": series_list,
                        "count": len(series_list),
                        "cached_at": int(__import__("time").time()),
                    }

                    # 測試 redis-py 內建的 json() 方法，自動序列化
                    await redis_client.json().set(cache_key, "$", cache_data)
                    await redis_client.expire(cache_key, SERIES_CACHE_TTL)
                    logger.debug(
                        "[SERIES_CACHE] 已緩存pool_type='%s'的%d個系列到Redis",
                        pool_type,
                        len(series_list),
                    )
                except Exception as e:
                    logger.warning(
                        "[SERIES_CACHE] 緩存pool_type='%s'失敗: %s", pool_type, e
                    )

        logger.debug(
            "[SERIES_CACHE] 從數據庫獲取 pool_type='%s' 的 %d 個系列",
            pool_type,
            len(series_list),
        )
        return series_list

    except Exception as e:
        logger.error(
            "[SERIES_CACHE] 獲取 pool_type='%s' 系列失敗: %s",
            pool_type,
            e,
            exc_info=True,
        )
        return []


async def clear_series_cache() -> None:
    """清除所有系列緩存（包括pool_type特定緩存）"""
    redis_client = get_redis_client()
    if redis_client:
        try:
            # 清除所有相關的緩存鍵
            pattern = f"{SERIES_CACHE_KEY}*"
            keys = await redis_client.keys(pattern)
            if keys:
                await redis_client.delete(*keys)
                logger.info("[SERIES_CACHE] 已清除 %d 個緩存鍵", len(keys))
            else:
                logger.info("[SERIES_CACHE] 沒有找到需要清除的緩存")
        except Exception as e:
            logger.warning("[SERIES_CACHE] 清除緩存失敗: %s", e)
