# gacha/services/leaderboard_service.py

from __future__ import annotations

import time
from datetime import datetime
from functools import lru_cache
from typing import Any, Optional

import discord

import gacha.repositories.leaderboard.leaderboard_repository as leaderboard_repo
from config.app_config import get_config
from database.postgresql.async_manager import get_redis_client
from gacha.exceptions import (
    InitialLeaderboardError,
    InvalidLeaderboardTypeError,
    LeaderboardQueryError,
    PlayerNotFoundError,
)
from utils.logger import logger

# --- 配置和輔助函數 ---
LEADERBOARD_CACHE_PREFIX = "gacha:leaderboard:"
LEADERBOARD_CACHE_TTL = 3600  # 1 hour
_cache_enabled = get_redis_client() is not None
items_per_page_config = get_config("gacha_core_settings.leaderboard_items_per_page", 10)
items_per_page = (
    int(items_per_page_config) if isinstance(items_per_page_config, (int, str)) else 10
)


def _generate_cache_key(**kwargs) -> str:
    leaderboard_type = kwargs.get("leaderboard_type", "unknown")
    page = kwargs.get("page", 1)
    key_parts = [leaderboard_type, f"page_{page}"]
    for key in ["asset_symbol", "game_type", "game_stat_type", "pool_type"]:
        if value := kwargs.get(key):
            if key == "pool_type" and value == "all":
                continue
            key_parts.append(f"{key.replace('_', '-')}_{value}")
    return LEADERBOARD_CACHE_PREFIX + ":".join(key_parts)


async def _get_leaderboard_from_cache(
    **kwargs,
) -> Optional[tuple[list[dict[str, Any]], int, int]]:
    if not _cache_enabled or kwargs.get("leaderboard_type") == "profile_likes":
        return None
    cache_key = _generate_cache_key(**kwargs)
    try:
        redis_client = get_redis_client()
        if not redis_client:
            return None
        import json
        cached_raw = await redis_client.execute_command("JSON.GET", cache_key)
        cached_data = json.loads(cached_raw) if cached_raw else None
        if cached_data:
            return (cached_data["results"], cached_data["total_pages"], cached_data["total_users"])
    except Exception as e:
        logger.warning("[LEADERBOARD_CACHE] 反序列化緩存數據失敗: %s", e)
    return None


def _json_serializer(obj):
    from datetime import date
    from decimal import Decimal

    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    elif isinstance(obj, Decimal):
        return float(obj)
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")


async def _cache_leaderboard_data(
    data: tuple[list[dict[str, Any]], int, int], **kwargs
) -> None:
    if not _cache_enabled or kwargs.get("leaderboard_type") == "profile_likes":
        return
    cache_key = _generate_cache_key(**kwargs)
    try:
        results, total_pages, total_users = data
        cache_data = {
            "results": results,
            "total_pages": total_pages,
            "total_users": total_users,
            "cached_at": time.time(),
        }
        redis_client = get_redis_client()
        if not redis_client:
            return
        import json
        await redis_client.execute_command(
            "JSON.SET", cache_key, "$", json.dumps(cache_data, default=_json_serializer)
        )
        await redis_client.expire(cache_key, LEADERBOARD_CACHE_TTL)
        logger.debug(
            "[LEADERBOARD_CACHE] 數據已緩存: %s, TTL=%ds",
            cache_key,
            LEADERBOARD_CACHE_TTL,
        )
    except Exception as e:
        logger.warning("[LEADERBOARD_CACHE] 緩存數據失敗: %s", e)


def _validate_leaderboard_type(leaderboard_type: str) -> None:
    if not leaderboard_type or not isinstance(leaderboard_type, str):
        raise InvalidLeaderboardTypeError(leaderboard_type)


def _enrich_results_with_rank(
    results: list[dict[str, Any]], page: int, current_items_per_page: int
) -> None:
    if not results:
        return
    start_rank = (page - 1) * current_items_per_page + 1
    for i, entry in enumerate(results, start_rank):
        entry["rank"] = i


# --- 從 LeaderboardCog 搬過來的核心業務邏輯 ---


async def get_leaderboard(**kwargs) -> tuple[list[dict[str, Any]], int, int]:
    """獲取排行榜數據，嚴格遵守錯誤處理規範。"""
    leaderboard_type = kwargs["leaderboard_type"]
    page = kwargs.get("page", 1)
    _validate_leaderboard_type(leaderboard_type)
    page = max(1, page)
    kwargs["page"] = page

    if _cache_enabled:
        if cached_data := await _get_leaderboard_from_cache(**kwargs):
            return cached_data

    # 移除 try-except 區塊，讓 repo 層的錯誤或任何非預期的錯誤自然冒泡
    is_profile_likes = leaderboard_type == "profile_likes"
    current_items_per_page = 1 if is_profile_likes else items_per_page

    kwargs["limit"] = current_items_per_page
    kwargs["offset"] = (page - 1) * current_items_per_page

    results, total_users = await leaderboard_repo.get_leaderboard(**kwargs)

    total_pages = (
        total_users
        if is_profile_likes
        else max(
            1, (total_users + current_items_per_page - 1) // current_items_per_page
        )
    )
    current_page = min(page, total_pages) if total_pages > 0 else 1

    _enrich_results_with_rank(results, current_page, current_items_per_page)

    kwargs["page"] = current_page
    if _cache_enabled:
        await _cache_leaderboard_data((results, total_pages, total_users), **kwargs)

    return results, total_pages, total_users


async def search_player_in_leaderboard(**kwargs) -> Optional[dict[str, Any]]:
    from utils.logger import logger

    leaderboard_type = kwargs["leaderboard_type"]
    user_id = kwargs.get("user_id")
    player_name = kwargs.get("player_name")

    logger.info(
        f"[LEADERBOARD_SERVICE] 開始搜索玩家: user_id={user_id}, player_name={player_name}, type={leaderboard_type}"
    )

    _validate_leaderboard_type(leaderboard_type)
    if user_id is None and not player_name:
        logger.warning("[LEADERBOARD_SERVICE] 搜索參數無效: user_id和player_name都為空")
        return None

    if user_id is not None:
        logger.info(f"[LEADERBOARD_SERVICE] 按用戶ID搜索: {user_id}")
        player_data = await leaderboard_repo.search_player_by_user_id(**kwargs)
    else:
        logger.info(f"[LEADERBOARD_SERVICE] 按玩家名稱搜索: {player_name}")
        player_data = await leaderboard_repo.search_player_by_name(**kwargs)

    if player_data is None:
        search_type = "ID" if user_id is not None else "名稱"
        search_input = user_id if user_id is not None else player_name
        logger.info(f"[LEADERBOARD_SERVICE] 未找到玩家: {search_type}={search_input}")
        raise PlayerNotFoundError(
            f"未在此排行榜找到{search_type}為「{search_input}」的玩家。"
        )

    logger.info("[LEADERBOARD_SERVICE] 搜索成功")
    return player_data


async def get_all_market_assets() -> list[dict[str, str]]:
    """從 Repository 獲取所有市場資產列表，並讓異常向上冒泡。"""
    return await leaderboard_repo.get_all_market_assets()


async def get_leaderboard_with_user_rank(**kwargs) -> dict[str, Any]:
    """
    並行獲取排行榜數據和用戶排名。
    如果用戶不在排行榜上，將 user_rank 設為 None 而不是拋出錯誤。
    """
    user_id = kwargs.get("user_id")

    leaderboard_task = get_leaderboard(**kwargs)

    # 獲取排行榜數據
    results_list, total_pages, total_users = await leaderboard_task

    # 嘗試獲取用戶排名，如果用戶不在排行榜上則設為 None
    user_rank_data = None
    if user_id:
        try:
            user_rank_data = await search_player_in_leaderboard(**kwargs)
        except PlayerNotFoundError:
            # 用戶不在此排行榜上，這是正常情況，不應該阻止排行榜顯示
            user_rank_data = None

    return {
        "results": results_list,
        "total_pages": total_pages,
        "total_users": total_users,
        "user_rank": user_rank_data,
        **kwargs,
    }


async def get_default_asset_symbol() -> Optional[str]:
    """獲取默認股票代碼，並讓異常向上冒泡。"""
    assets = await get_all_market_assets()
    return assets[0]["symbol"] if assets else None


def get_default_game_stat_type() -> str:
    return "total_profit_loss"


@lru_cache(maxsize=1)
def get_leaderboard_config() -> dict[str, Any]:
    """快取排行榜配置以避免重複讀取文件。"""
    config = get_config("gacha_core_settings.leaderboard_config", {})
    return config if isinstance(config, dict) else {}


def get_first_leaderboard_type_for_category(category: str) -> Optional[str]:
    """獲取指定分類下的默認排行榜類型，優先返回標記為 default_for_category 的類型"""
    leaderboard_config = get_leaderboard_config()

    # 首先尋找標記為 default_for_category 的排行榜類型
    default_type = None
    fallback_type = None

    for type_key, config_data in leaderboard_config.items():
        if hasattr(config_data, "category") and config_data.category == category:
            if not fallback_type:
                fallback_type = type_key  # 記錄第一個找到的類型作為備選
            if (
                hasattr(config_data, "default_for_category")
                and config_data.default_for_category
            ):
                default_type = type_key
                break  # 找到默認類型就立即返回

    return default_type or fallback_type


async def get_initial_leaderboard_data(category: str, user_id: int) -> dict[str, Any]:
    """
    獲取並驗證初始排行榜數據。
    如果無法獲取，將引發 InitialLeaderboardError。
    """
    leaderboard_type = get_first_leaderboard_type_for_category(category)
    if not leaderboard_type:
        raise InitialLeaderboardError(f"錯誤：找不到分類 '{category}' 的排行榜類型。")

    params = {
        "leaderboard_type": leaderboard_type,
        "asset_symbol": None,
        "game_type": None,
        "game_stat_type": None,
        "pool_type": "all",
    }

    if leaderboard_type == "stock_holding":
        params["asset_symbol"] = await get_default_asset_symbol()
        if not params["asset_symbol"]:
            raise InitialLeaderboardError("市場上沒有可用的股票，無法顯示股票排行榜。")

    from gacha.core.game_registry import GameRegistry

    if GameRegistry.is_valid_game_type(leaderboard_type):
        params["game_type"] = leaderboard_type
        params["game_stat_type"] = get_default_game_stat_type()

    # 獲取數據
    start_time = time.time()
    data = await get_leaderboard_with_user_rank(page=1, user_id=user_id, **params)

    # 驗證數據
    if not data or not data.get("results"):
        # 對於遊戲排行榜，沒有數據是可接受的初始狀態
        if not GameRegistry.is_valid_game_type(leaderboard_type):
            raise LeaderboardQueryError("此排行榜目前沒有任何數據。")

    data["query_time"] = time.time() - start_time
    data["updated_at"] = datetime.now()
    data["initial_params"] = params
    data["initial_category"] = category
    return data


def create_loading_embed(leaderboard_type: str) -> discord.Embed:
    """創建載入中的嵌入消息"""
    leaderboard_config = get_leaderboard_config()
    config_setting = leaderboard_config.get(leaderboard_type)

    title = (
        config_setting.title
        if config_setting
        else f"{leaderboard_type.replace('_', ' ').title()} 排行榜"
    )
    description = (
        config_setting.description if config_setting else "正在載入排行榜數據..."
    )
    color_val = config_setting.color if config_setting else 7506394

    return discord.Embed(
        title=title,
        description=f"{description}\n\n⏳ **載入中，請稍候...**",
        color=discord.Color(color_val),
    )
