import asyncio
import json
import random
import re
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import Any, Dict, Optional

import asyncpg

from auxiliary.services.ai_core import ai_service, prompt_service
from config.app_config import (
    get_character_archetype_readable_names,
    get_gacha_stock_integration_config,
)
from database.postgresql.async_manager import get_pool, get_redis_client
from gacha.models.market_models import StockLifecycleStatus
from gacha.services import anchor_price_service
from utils.logger import logger

news_type_definitions = {
    "PTT_ANALYSIS": {"archetypes": ["PTT_USER"], "impacts_price": False},
    "CITIZEN_STORY": {"archetypes": ["ORDINARY_CITIZEN"], "impacts_price": False},
    "INSIDER_TIP": {"archetypes": ["MYSTERIOUS_INFORMANT"], "impacts_price": True},
    "ANALYST_REPORT": {"archetypes": ["MARKET_ANALYST"], "impacts_price": False},
    "CORPORATE_ANNOUNCEMENT": {
        "archetypes": ["CORPORATE_SPOKESPERSON", "OFFICIAL_PRESS_RELEASE"],
        "impacts_price": True,
    },
    "REGULATORY_CHANGE": {
        "archetypes": ["REGULATORY_BODY_BULLETIN", "INDUSTRY_ANALYST_INTERPRETATION"],
        "impacts_price": True,
    },
    "MARKET_RUMOR": {"archetypes": ["GOSSIP_MONGER"], "impacts_price": False},
    "TECHNICAL_SIGNAL": {"archetypes": ["TA_BOT"], "impacts_price": False},
    "GENERAL_MARKET_NEWS": {
        "archetypes": ["AI_NEWSCASTER_STANDARD"],
        "impacts_price": True,
    },
    "STOCK_ST_WARNING": {
        "prompt_template_key": "lifecycle_st_warning_prompt",
        "archetypes": ["SYSTEM_ANNOUNCER"],
        "impacts_price": False,
    },
    "STOCK_ST_RECOVERY": {
        "prompt_template_key": "lifecycle_st_recovery_prompt",
        "archetypes": ["SYSTEM_ANNOUNCER"],
        "impacts_price": False,
    },
    "STOCK_DELISTED": {
        "prompt_template_key": "lifecycle_delisted_prompt",
        "archetypes": ["SYSTEM_ANNOUNCER"],
        "impacts_price": False,
    },
    "STOCK_NEW_LISTING": {
        "prompt_template_key": "lifecycle_new_listing_prompt",
        "archetypes": ["MARKET_ANALYST"],
        "impacts_price": False,
    },
}
_stock_config = get_gacha_stock_integration_config().stock_market
min_asset_price = Decimal(str(getattr(_stock_config, "global_min_asset_price", "1.0")))
lifecycle_event_types = [
    "STOCK_ST_WARNING",
    "STOCK_ST_RECOVERY",
    "STOCK_DELISTED",
    "STOCK_NEW_LISTING",
]


def _parse_ai_news_response(
    raw_ai_text: str, news_type_for_log: str
) -> Dict[str, Optional[str]]:
    parsed_data: Dict[str, Optional[str]] = {}
    default_headline = "AI生成新聞標題失敗"
    default_content = "AI未能按預期格式提供內容"
    ai_payload_missing_error_prefix = "錯誤：AI回應未包含期望的 <ai_payload> 標籤"
    if raw_ai_text.startswith(ai_payload_missing_error_prefix):
        logger.error(
            "AI response for '%s' was an error from AIService (likely missing <ai_payload>): '%s'",
            news_type_for_log,
            raw_ai_text,
        )
        parsed_data["headline"] = default_headline
        parsed_data["content"] = default_content
        return parsed_data
    if not raw_ai_text or "無法獲取回答" in raw_ai_text or len(raw_ai_text.strip()) < 5:
        logger.error(
            "AI failed to generate valid content for '%s'. Response: '%s'",
            news_type_for_log,
            raw_ai_text,
        )
        parsed_data["headline"] = default_headline
        parsed_data["content"] = default_content
        return parsed_data
    xml_to_parse = raw_ai_text.strip()

    ai_payload_match = re.search(
        "<ai_payload>(.*?)</ai_payload>", xml_to_parse, re.DOTALL | re.IGNORECASE
    )
    if ai_payload_match:
        xml_to_parse = ai_payload_match.group(1).strip()
        logger.debug(
            "Extracted content from <ai_payload> wrapper for %s", news_type_for_log
        )

    if not (xml_to_parse.startswith("<news>") and xml_to_parse.endswith("</news>")):
        news_match = re.search(
            "<news>(.*?)</news>", xml_to_parse, re.DOTALL | re.IGNORECASE
        )
        if news_match:
            xml_to_parse = f"<news>{news_match.group(1).strip()}</news>"
            logger.warning(
                "AI response for %s had content outside <news> tags. Cleaned: %s...",
                news_type_for_log,
                xml_to_parse[:200],
            )
        else:
            logger.warning(
                "Raw AI text for %s does not appear to be a <news> XML structure even after regex. Raw: %s...",
                news_type_for_log,
                xml_to_parse[:200],
            )
            parsed_data["headline"] = default_headline
            parsed_data["content"] = xml_to_parse if xml_to_parse else default_content
            return parsed_data
    try:
        known_tags_to_extract = [
            "headline",
            "content",
            "reason",
            "current_price",
            "final_buyback_price",
            "initial_price",
            "total_shares",
            "linked_criteria_description",
            "impact_magnitude_percent",
            "impact_direction",
            "indicator_data",
            "signal_interpretation",
        ]
        for tag_name in known_tags_to_extract:
            pattern = f"<{tag_name}>(.*?)</{tag_name}>"
            match = re.search(pattern, xml_to_parse, re.DOTALL | re.IGNORECASE)
            if match:
                text_content = match.group(1).strip()
                if tag_name == "headline":
                    parsed_data[tag_name] = text_content[:250]
                else:
                    parsed_data[tag_name] = text_content
        if not parsed_data.get("headline") and (not parsed_data.get("content")):
            if xml_to_parse.startswith("<news>"):
                logger.warning(
                    "Could not extract headline or content using regex from supposed <news> block for %s. Raw <news> block: %s...",
                    news_type_for_log,
                    xml_to_parse[:200],
                )
    except Exception as e_general:
        logger.error(
            "Unexpected error during regex news parsing for %s: %s. Raw: %s...",
            news_type_for_log,
            e_general,
            xml_to_parse[:200],
            exc_info=True,
        )
    if not parsed_data.get("headline"):
        parsed_data["headline"] = default_headline
        if raw_ai_text:
            logger.warning(
                "Headline for %s defaulted after parsing attempts. Raw: %s...",
                news_type_for_log,
                raw_ai_text[:200],
            )
    if not parsed_data.get("content"):
        if parsed_data.get("headline") != default_headline:
            parsed_data["content"] = parsed_data["headline"]
        else:
            parsed_data["content"] = default_content
        if raw_ai_text:
            logger.warning(
                "Content for %s defaulted or used headline after parsing attempts. Raw: %s...",
                news_type_for_log,
                raw_ai_text[:200],
            )
    return parsed_data


async def news_scheduler_manager():
    logger.debug("News scheduler manager executing...")
    stock_config = _stock_config
    default_interval = stock_config.default_news_type_generation_interval_minutes
    specific_intervals = stock_config.news_type_generation_intervals_minutes
    pool = get_pool()
    if pool is None:
        logger.error("Database pool is not initialized.")
        return
    async with pool.acquire() as conn:
        for news_type, _ in news_type_definitions.items():
            try:
                if news_type in lifecycle_event_types:
                    logger.debug(
                        "Skipping lifecycle news type '%s' in scheduler manager as it's triggered elsewhere.",
                        news_type,
                    )
                    continue
                generation_interval_minutes = specific_intervals.get(
                    news_type, default_interval
                )
                last_gen_record = await conn.fetchrow(
                    "SELECT last_generated_at FROM news_type_generation_log WHERE news_type = $1",
                    news_type,
                )
                last_generated_at = None
                if last_gen_record and last_gen_record["last_generated_at"]:
                    last_generated_at = last_gen_record["last_generated_at"]
                    if last_generated_at.tzinfo is None:
                        last_generated_at = last_generated_at.replace(
                            tzinfo=timezone.utc
                        )
                ready_to_generate = False
                if last_generated_at is None:
                    ready_to_generate = True
                    logger.info(
                        "News type '%s' never generated. Scheduling for generation.",
                        news_type,
                    )
                else:
                    current_time_utc = datetime.now(timezone.utc)
                    time_since_last_generation = current_time_utc - last_generated_at
                    if time_since_last_generation >= timedelta(
                        minutes=generation_interval_minutes
                    ):
                        ready_to_generate = True
                        logger.info(
                            "News type '%s' ready for generation. Last: %s, Interval: %sm.",
                            news_type,
                            last_generated_at,
                            generation_interval_minutes,
                        )
                    else:
                        logger.debug(
                            "News type '%s' not yet ready. Last: %s, Interval: %sm, Remaining: %s",
                            news_type,
                            last_generated_at,
                            generation_interval_minutes,
                            timedelta(minutes=generation_interval_minutes)
                            - time_since_last_generation,
                        )
                if ready_to_generate:
                    logger.info(
                        "Creating task to generate news for type: %s", news_type
                    )
                    asyncio.create_task(_generate_specific_news_type(news_type))
            except Exception as e:
                logger.error(
                    "Error in news_scheduler_manager for news_type '%s': %s",
                    news_type,
                    e,
                    exc_info=True,
                )
    logger.debug("News scheduler manager finished cycle.")


async def _generate_specific_news_type(news_type_to_generate: str):
    logger.info("Executing _generate_specific_news_type for: %s", news_type_to_generate)
    if news_type_to_generate in lifecycle_event_types:
        logger.info(
            "Skipping news generation for lifecycle event '%s' in _generate_specific_news_type. It should be triggered by StockLifecycleService.",
            news_type_to_generate,
        )
        try:
            pool = get_pool()
            if pool is None:
                logger.error("Database pool is not initialized.")
                return
            async with pool.acquire() as conn:
                await conn.execute(
                    "INSERT INTO news_type_generation_log (news_type, last_generated_at) VALUES ($1, CURRENT_TIMESTAMP) ON CONFLICT (news_type) DO UPDATE SET last_generated_at = CURRENT_TIMESTAMP",
                    news_type_to_generate,
                )
            logger.info(
                "Updated news_type_generation_log for skipped lifecycle event '%s' to prevent immediate re-trigger by scheduler.",
                news_type_to_generate,
            )
        except Exception as e_log:
            logger.error(
                "Failed to update news_type_generation_log for skipped lifecycle event '%s': %s",
                news_type_to_generate,
                e_log,
            )
        return
    stock_config = _stock_config
    news_impact_config = stock_config.news_impact
    temp_effect_config = stock_config.temp_news_effect

    type_def_data = news_type_definitions.get(news_type_to_generate)
    if not type_def_data:
        logger.error(
            "News type definition for '%s' not found. Skipping.", news_type_to_generate
        )
        return
    pool = get_pool()
    if pool is None:
        logger.error("Database pool is not initialized.")
        return
    async with pool.acquire() as conn:
        try:
            archetypes = type_def_data.get("archetypes", [])
            if not isinstance(archetypes, list) or not archetypes:
                logger.error(
                    f"No valid archetypes found for news type {news_type_to_generate}"
                )
                return
            chosen_character_archetype = random.choice(archetypes)
            impacts_price = type_def_data.get("impacts_price", False)
            target_asset_id = None
            target_asset_symbol = "GLOBAL"
            target_asset_name = "整個市場"
            selected_stock_details = None
            prompt_data = {}
            raw_data_input_for_db = {}
            should_target_specific_stock = True
            if news_type_to_generate in ["ANALYST_REPORT", "REGULATORY_CHANGE"]:
                if random.random() > 0.6:
                    should_target_specific_stock = False
            if news_type_to_generate == "GENERAL_MARKET_NEWS":
                should_target_specific_stock = False
            if should_target_specific_stock:
                active_assets_query = """
                    SELECT asset_id, asset_symbol, asset_name, current_price,
                           base_volatility, volatility_factor,
                           linked_criteria_type, linked_criteria_value, linked_pool_context,
                           initial_anchor_price, current_anchor_price, anchor_price_updated_at
                    FROM virtual_assets
                    WHERE (current_price > 0 OR current_price IS NULL)
                          AND (initial_anchor_price IS NOT NULL OR current_anchor_price IS NOT NULL)
                          AND lifecycle_status != $1
                """
                active_asset_ids_records = await conn.fetch(
                    active_assets_query, StockLifecycleStatus.DELISTED.value
                )
                if active_asset_ids_records:
                    selected_stock_details = random.choice(active_asset_ids_records)
                    target_asset_id = selected_stock_details["asset_id"]
                    target_asset_symbol = selected_stock_details["asset_symbol"]
                    target_asset_name = selected_stock_details["asset_name"]
                    asset_cooldown_minutes = stock_config.asset_news_cooldown_minutes
                    if asset_cooldown_minutes > 0 and target_asset_id:
                        last_asset_news_time_record = await conn.fetchrow(
                            "SELECT MAX(published_at) as last_news_time\n                                   FROM market_news\n                                   WHERE affected_asset_id = $1 AND news_type = $2",
                            target_asset_id,
                            news_type_to_generate,
                        )
                        if (
                            last_asset_news_time_record
                            and last_asset_news_time_record["last_news_time"]
                        ):
                            last_asset_news_time_aware = last_asset_news_time_record[
                                "last_news_time"
                            ]
                            if last_asset_news_time_aware.tzinfo is None:
                                last_asset_news_time_aware = (
                                    last_asset_news_time_aware.replace(
                                        tzinfo=timezone.utc
                                    )
                                )
                            current_utc_time_asset_cd = datetime.now(timezone.utc)
                            time_since_last_asset_news = (
                                current_utc_time_asset_cd - last_asset_news_time_aware
                            )
                            if time_since_last_asset_news < timedelta(
                                minutes=asset_cooldown_minutes
                            ):
                                logger.info(
                                    "Asset ID %s ('%s') for news type '%s' is in asset-specific cooldown (%sm). Last news: %s. Skipping.",
                                    target_asset_id,
                                    target_asset_name,
                                    news_type_to_generate,
                                    asset_cooldown_minutes,
                                    last_asset_news_time_aware,
                                )
                                return
                elif should_target_specific_stock:
                    logger.warning(
                        "News type '%s' intended for specific stock, but no active stocks found. Skipping.",
                        news_type_to_generate,
                    )
                    return
            logger.info(
                "Proceeding to generate '%s' for asset: %s (ID: %s)",
                news_type_to_generate,
                target_asset_name,
                target_asset_id,
            )
            raw_data_input_for_db["target_asset_id"] = target_asset_id
            raw_data_input_for_db["target_asset_symbol"] = target_asset_symbol
            raw_data_input_for_db["target_asset_name"] = target_asset_name
            sentiments = ["positive", "negative", "neutral"]
            chosen_sentiment = random.choices(
                sentiments, weights=[0.45, 0.45, 0.1], k=1
            )[0]
            prompt_data["sentiment"] = chosen_sentiment
            raw_data_input_for_db["chosen_sentiment"] = chosen_sentiment
            if news_type_to_generate == "PTT_ANALYSIS" and selected_stock_details:
                price_a = selected_stock_details["current_price"] * Decimal(
                    str(random.uniform(0.9, 1.1))
                )
                price_b = selected_stock_details["current_price"]
                change_direction = (
                    "漲" if price_b > price_a else "跌" if price_b < price_a else "持平"
                )
                market_target_value = (
                    f"{target_asset_name} ({target_asset_symbol})"
                    if target_asset_id
                    else target_asset_name
                )
                prompt_data["market_target"] = market_target_value
                prompt_data["price_a"] = f"{price_a:.2f}"
                prompt_data["price_b"] = f"{price_b:.2f}"
                prompt_data["change_direction"] = change_direction
                raw_data_input_for_db.update(
                    {
                        "market_target": market_target_value,
                        "price_a": f"{price_a:.2f}",
                        "price_b": f"{price_b:.2f}",
                        "change_direction": change_direction,
                    }
                )
            elif (
                news_type_to_generate == "CORPORATE_ANNOUNCEMENT"
                and selected_stock_details
            ):
                market_target_value = (
                    f"{target_asset_name} ({target_asset_symbol})"
                    if target_asset_id
                    else target_asset_name
                )
                prompt_data["market_target"] = market_target_value
                event_types = [
                    "新產品發布",
                    "財務預測調整",
                    "併購案",
                    "高層人事變動",
                    "重大合約簽訂",
                    "研發突破",
                ]
                prompt_data["event_type"] = random.choice(event_types)
                prompt_data["event_details"] = (
                    f"關於{prompt_data['event_type']}的進一步細節，市場正密切關注。"
                )
                if chosen_character_archetype == "OFFICIAL_PRESS_RELEASE":
                    prompt_data["press_release_subject"] = (
                        f"{target_asset_name}發布關於{prompt_data['event_type']}的重要聲明"
                    )
                    prompt_data["core_information"] = prompt_data["event_details"]
                raw_data_input_for_db.update(
                    {
                        "market_target": market_target_value,
                        "event_type": prompt_data.get("event_type"),
                        "event_details": prompt_data.get("event_details"),
                        "press_release_subject": prompt_data.get(
                            "press_release_subject"
                        ),
                        "core_information": prompt_data.get("core_information"),
                    }
                )
            elif news_type_to_generate == "INSIDER_TIP":
                market_target_value = (
                    f"{target_asset_name} ({target_asset_symbol})"
                    if target_asset_id
                    else target_asset_name
                )
                prompt_data["market_target"] = market_target_value
                tip_ideas = [
                    "將有重大利好",
                    "主力可能正在暗中吸籌",
                    "近期可能有未公開消息",
                ]
                if chosen_sentiment == "negative":
                    tip_ideas = ["恐有潛在利空", "主力可能正在出貨", "近期需留意風險"]
                elif chosen_sentiment == "neutral":
                    tip_ideas = ["市場方向未明", "多空持續觀望", "短期可能盤整"]
                prompt_data["tip_core_idea"] = random.choice(tip_ideas)
                raw_data_input_for_db.update(
                    {
                        "market_target": market_target_value,
                        "tip_core_idea": prompt_data.get("tip_core_idea"),
                    }
                )
            elif news_type_to_generate == "CITIZEN_STORY":
                market_target_value = (
                    f"{target_asset_name} ({target_asset_symbol})"
                    if target_asset_id
                    else target_asset_name
                )
                prompt_data["market_target"] = market_target_value
                story_contexts_positive = [
                    f"聽朋友說 {target_asset_name} 會漲，結果真的小賺一筆！",
                    f"最近手氣不錯，買的 {target_asset_name} 漲了不少。",
                ]
                story_contexts_negative = [
                    f"隔壁老王推薦我買 {target_asset_name}，結果跌到吃土。",
                    f"不懂股票亂買 {target_asset_name}，賠了一個月零用錢。",
                ]
                story_contexts_neutral = [
                    f"今天 {target_asset_name} 沒什麼動靜。",
                    f"對 {target_asset_name} 沒什麼特別感覺。",
                ]
                if chosen_sentiment == "positive":
                    prompt_data["story_context"] = random.choice(
                        story_contexts_positive
                    )
                elif chosen_sentiment == "negative":
                    prompt_data["story_context"] = random.choice(
                        story_contexts_negative
                    )
                else:
                    prompt_data["story_context"] = random.choice(story_contexts_neutral)
                raw_data_input_for_db.update(
                    {
                        "market_target": market_target_value,
                        "story_context": prompt_data.get("story_context"),
                    }
                )
            elif news_type_to_generate == "ANALYST_REPORT":
                if selected_stock_details:
                    current_price = selected_stock_details["current_price"]
                    base_volatility = selected_stock_details["base_volatility"]
                    volatility_factor = selected_stock_details["volatility_factor"]
                else:
                    market_aggregates = await _calculate_market_aggregates(conn)
                    current_price = market_aggregates["avg_price"]
                    base_volatility = market_aggregates["avg_base_volatility"]
                    volatility_factor = market_aggregates["avg_volatility_factor"]

                prompt_data.update(
                    {
                        "report_date": datetime.now(timezone.utc).strftime("%Y-%m-%d"),
                        "market_overview": await _generate_market_overview(
                            conn, selected_stock_details
                        ),
                        "stock_name_1": target_asset_name,
                        "stock_symbol_1": target_asset_symbol,
                        "current_price_1": f"{current_price:.4f}",
                        "base_volatility_1": f"{base_volatility:.3f}",
                        "volatility_factor_1": f"{volatility_factor:.2f}",
                        "player_activity_summary": await _generate_player_activity_summary(
                            conn
                        ),
                    }
                )
                raw_data_input_for_db.update(
                    {
                        k: prompt_data.get(k)
                        for k in [
                            "report_date",
                            "market_overview",
                            "stock_name_1",
                            "stock_symbol_1",
                            "current_price_1",
                            "base_volatility_1",
                            "volatility_factor_1",
                            "player_activity_summary",
                        ]
                    }
                )
            elif news_type_to_generate == "REGULATORY_CHANGE":
                policies = ["新能源補貼調整", "金融科技監管強化", "半導體出口管制"]
                prompt_data.update(
                    {
                        "policy_name": random.choice(policies),
                        "affected_industry": random.choice(
                            ["新能源汽車", "金融科技", "半導體"]
                        ),
                        "policy_content": "最新政策旨在促進市場健康發展。",
                        "effective_date": (
                            datetime.now(timezone.utc)
                            + timedelta(days=random.randint(7, 30))
                        ).strftime("%Y-%m-%d"),
                    }
                )
                if chosen_character_archetype == "REGULATORY_BODY_BULLETIN":
                    prompt_data["issuing_authority"] = random.choice(
                        ["金融監督管理委員會虛擬分部", "產業發展署模擬辦公室"]
                    )
                elif chosen_character_archetype == "INDUSTRY_ANALYST_INTERPRETATION":
                    prompt_data["bulletin_summary"] = (
                        f"監管機構發布了{prompt_data['policy_name']}。"
                    )
                raw_data_input_for_db.update(
                    {
                        k: prompt_data.get(k)
                        for k in [
                            "policy_name",
                            "affected_industry",
                            "policy_content",
                            "effective_date",
                            "issuing_authority",
                            "bulletin_summary",
                        ]
                    }
                )
            elif news_type_to_generate == "MARKET_RUMOR":
                subj = (
                    [
                        f"聽說 {target_asset_name} 可能有利好",
                        f"市場傳言 {target_asset_name} 將有大動作",
                    ]
                    if chosen_sentiment == "positive"
                    else (
                        [
                            f"小道消息說 {target_asset_name} 最近可能有麻煩",
                            f"市場擔心 {target_asset_name} 的前景",
                        ]
                        if chosen_sentiment == "negative"
                        else [f"關於 {target_asset_name} 的傳聞滿天飛"]
                    )
                )
                prompt_data.update(
                    {
                        "rumor_subject": random.choice(subj),
                        "rumor_core_details": "具體細節尚不明朗。",
                        "source_hint": random.choice(
                            ["聽隔壁桌大戶說的", "內部人士透露"]
                        ),
                    }
                )
                raw_data_input_for_db.update(
                    {
                        k: prompt_data.get(k)
                        for k in ["rumor_subject", "rumor_core_details", "source_hint"]
                    }
                )
            elif news_type_to_generate == "TECHNICAL_SIGNAL":
                obs = (
                    ["出現黃金交叉", "RSI超賣反彈"]
                    if chosen_sentiment == "positive"
                    else (
                        ["出現死亡交叉", "RSI超買"]
                        if chosen_sentiment == "negative"
                        else ["均線糾結", "RSI中軸徘徊"]
                    )
                )
                interp = (
                    ["短期看多"]
                    if chosen_sentiment == "positive"
                    else (
                        ["短期看空"] if chosen_sentiment == "negative" else ["盤整格局"]
                    )
                )
                market_target_value = (
                    f"{target_asset_name} ({target_asset_symbol})"
                    if target_asset_id
                    else target_asset_name
                )

                current_price = (
                    selected_stock_details["current_price"]
                    if selected_stock_details
                    else Decimal("0")
                )
                ma5_price = current_price * Decimal(str(random.uniform(0.98, 1.02)))
                rsi_value = (
                    random.randint(30, 70)
                    if chosen_sentiment == "neutral"
                    else (
                        random.randint(70, 85)
                        if chosen_sentiment == "positive"
                        else random.randint(15, 30)
                    )
                )
                indicator_data = f"MA5: {ma5_price:.4f}, RSI: {rsi_value}"

                prompt_data.update(
                    {
                        "market_target": market_target_value,
                        "timeframe": random.choice(["日線圖", "週線圖"]),
                        "technical_observation": random.choice(obs),
                        "signal_interpretation": random.choice(interp),
                        "indicator_data": indicator_data,
                        "current_price": f"{current_price:.4f}",
                    }
                )
                raw_data_input_for_db.update(
                    {
                        k: prompt_data.get(k)
                        for k in [
                            "market_target",
                            "timeframe",
                            "technical_observation",
                            "indicator_data",
                            "signal_interpretation",
                            "current_price",
                        ]
                    }
                )
            elif news_type_to_generate == "GENERAL_MARKET_NEWS":
                if target_asset_id:
                    market_target_value = f"{target_asset_name} ({target_asset_symbol})"
                else:
                    market_target_value = target_asset_name
                prompt_data.update(
                    {
                        "market_target": market_target_value,
                        "headline": (
                            f"{target_asset_name} 動態"
                            if target_asset_id
                            else "市場概況"
                        ),
                        "summary": "最新摘要。",
                        "related_data": "波動率微升。",
                    }
                )
                raw_data_input_for_db.update(
                    {
                        k: prompt_data.get(k)
                        for k in [
                            "market_target",
                            "headline",
                            "summary",
                            "related_data",
                        ]
                    }
                )
            dampening_factor = Decimal("1.0")
            if (
                target_asset_id
                and impacts_price
                and (stock_config.sentiment_inertia_window_minutes > 0)
                and (stock_config.strong_news_impact_threshold > 0)
            ):
                recent_news = await conn.fetchrow(
                    "SELECT sentiment FROM market_news WHERE affected_asset_id = $1 AND published_at >= $2 AND impacts_price = TRUE ORDER BY published_at DESC LIMIT 1",
                    target_asset_id,
                    datetime.now(timezone.utc)
                    - timedelta(minutes=stock_config.sentiment_inertia_window_minutes),
                )
                if recent_news and (
                    chosen_sentiment == "positive"
                    and recent_news["sentiment"] == "negative"
                    or (
                        chosen_sentiment == "negative"
                        and recent_news["sentiment"] == "positive"
                    )
                ):
                    dampening_factor = Decimal(
                        str(stock_config.conflicting_news_dampening_factor)
                    )
                    logger.info(
                        "Sentiment conflict for asset %s. Dampening: %s",
                        target_asset_id,
                        dampening_factor,
                    )
            formatted_prompts = prompt_service.get_formatted_prompt(
                news_type_to_generate, chosen_character_archetype, prompt_data
            )
            if not formatted_prompts:
                logger.error(
                    "無法獲取提示詞: %s, %s",
                    news_type_to_generate,
                    chosen_character_archetype,
                )
                return
            ai_response = await ai_service.process_text(
                prompt=formatted_prompts[1], system_prompt=formatted_prompts[0]
            )
            raw_ai_text = ai_service.extract_response_text(ai_response)
            if (
                not raw_ai_text
                or "無法獲取回答" in raw_ai_text
                or len(raw_ai_text.strip()) < 10
            ):
                logger.error(
                    "AI未能為'%s'生成有效新聞. 回應: '%s'",
                    news_type_to_generate,
                    raw_ai_text,
                )
                return
            parsed_data = _parse_ai_news_response(raw_ai_text, news_type_to_generate)
            headline = parsed_data["headline"]
            content = parsed_data["content"]
            if headline == "AI生成新聞標題失敗" or content == "AI生成新聞內容失敗":
                logger.error(
                    "Critical failure in parsing AI response for %s. Headline: '%s', Content: '%s'",
                    news_type_to_generate,
                    headline,
                    content,
                )
                return
            char_name = get_character_archetype_readable_names().get(
                chosen_character_archetype, chosen_character_archetype
            )
            if chosen_character_archetype == "PTT_USER":
                char_name = f"鄉民{random.choice(['大神', '韭菜'])}"
            raw_data_input_for_db["_ai_parsed_response_"] = parsed_data
            async with conn.transaction():
                market_news_id = await conn.fetchval(
                    "INSERT INTO market_news (headline, content, affected_asset_id, sentiment, source, news_type, character_archetype, character_name, impacts_price, raw_data_input)\n                           VALUES ($1, $2, $3, $4, 'AI_NEWS_GENERATOR', $5, $6, $7, $8, $9)\n                           RETURNING id",
                    headline,
                    content,
                    target_asset_id,
                    chosen_sentiment,
                    news_type_to_generate,
                    chosen_character_archetype,
                    char_name,
                    impacts_price,
                    raw_data_input_for_db,
                )
                logger.info(
                    "儲存新聞: Type=%s, AssetID=%s, NewsID=%s",
                    news_type_to_generate,
                    target_asset_id,
                    market_news_id,
                )
                await conn.execute(
                    "INSERT INTO news_type_generation_log (news_type, last_generated_at) VALUES ($1, CURRENT_TIMESTAMP) ON CONFLICT (news_type) DO UPDATE SET last_generated_at = CURRENT_TIMESTAMP",
                    news_type_to_generate,
                )
                logger.info(
                    "更新 news_type_generation_log for '%s'.", news_type_to_generate
                )
                if (
                    impacts_price
                    and target_asset_id
                    and selected_stock_details
                    and market_news_id
                ):
                    effective_anchor_price = (
                        await anchor_price_service.get_effective_anchor_price(
                            conn,
                            target_asset_id,
                            selected_stock_details["initial_anchor_price"],
                            selected_stock_details["current_anchor_price"],
                            selected_stock_details["anchor_price_updated_at"],
                        )
                    )
                    if effective_anchor_price <= Decimal("0"):
                        logger.error(
                            "AssetID %s (News Event Queuing): Effective anchor price is %s. Skipping news impact queuing.",
                            target_asset_id,
                            effective_anchor_price,
                        )
                    else:
                        impact_pct_decimal = Decimal("0.0")
                        ai_impact_magnitude_percent_str = parsed_data.get(
                            "impact_magnitude_percent"
                        )
                        ai_impact_direction_str = parsed_data.get("impact_direction")
                        if ai_impact_magnitude_percent_str and ai_impact_direction_str:
                            try:
                                magnitude_percent_val = Decimal(
                                    ai_impact_magnitude_percent_str
                                )
                                direction_sign = Decimal("0.0")
                                if ai_impact_direction_str.lower() == "positive":
                                    direction_sign = Decimal("1")
                                elif ai_impact_direction_str.lower() == "negative":
                                    direction_sign = Decimal("-1")
                                elif ai_impact_direction_str == "1":
                                    direction_sign = Decimal("1")
                                elif ai_impact_direction_str == "-1":
                                    direction_sign = Decimal("-1")
                                if direction_sign != Decimal("0.0"):
                                    impact_pct_decimal = (
                                        magnitude_percent_val
                                        / Decimal("100")
                                        * direction_sign
                                    )
                                    logger.info(
                                        "Using AI-provided impact for news %s: Magnitude %s%%, Direction %s. Resulting factor: %s",
                                        market_news_id,
                                        magnitude_percent_val,
                                        ai_impact_direction_str,
                                        impact_pct_decimal,
                                    )
                                else:
                                    logger.warning(
                                        "AI-provided impact direction '%s' for news %s is invalid. Falling back to sentiment-based impact.",
                                        ai_impact_direction_str,
                                        market_news_id,
                                    )
                                    impact_pct_decimal = Decimal("0.0")
                            except ValueError:
                                logger.warning(
                                    "Could not parse AI-provided impact_magnitude_percent '%s' as Decimal for news %s. Falling back to sentiment-based impact.",
                                    ai_impact_magnitude_percent_str,
                                    market_news_id,
                                )
                                impact_pct_decimal = Decimal("0.0")
                        if impact_pct_decimal == Decimal("0.0"):
                            logger.info(
                                "Falling back to sentiment-based random impact for news %s (chosen_sentiment: %s).",
                                market_news_id,
                                chosen_sentiment,
                            )
                            temp_impact_pct_float = 0.0
                            if chosen_sentiment == "positive":
                                temp_impact_pct_float = random.uniform(
                                    float(news_impact_config.positive_min),
                                    float(news_impact_config.positive_max),
                                )
                            elif chosen_sentiment == "negative":
                                temp_impact_pct_float = random.uniform(
                                    float(news_impact_config.negative_min),
                                    float(news_impact_config.negative_max),
                                )
                            impact_pct_decimal = Decimal(str(temp_impact_pct_float))
                        if impact_pct_decimal != Decimal("0.0"):
                            vol_factor = Decimal(
                                str(selected_stock_details["volatility_factor"])
                            )
                            current_price = Decimal(
                                str(selected_stock_details["current_price"])
                            )
                            eff_impact_amount = (
                                current_price
                                * impact_pct_decimal
                                * vol_factor
                                * dampening_factor
                            )
                            pending_event_data = {
                                "news_id": market_news_id,
                                "impact_amount_str": str(
                                    eff_impact_amount.quantize(Decimal("0.000001"))
                                ),
                                "news_type": news_type_to_generate,
                                "sentiment": chosen_sentiment,
                                "ai_provided_impact": bool(
                                    ai_impact_magnitude_percent_str
                                    and ai_impact_direction_str
                                ),
                                "generated_at_iso": datetime.now(
                                    timezone.utc
                                ).isoformat(),
                            }
                            redis_key = f"pending_news_impacts:{target_asset_id}"
                            try:
                                redis_client = get_redis_client()
                                if redis_client:
                                    await redis_client.rpush(  # type: ignore
                                        redis_key, json.dumps(pending_event_data)
                                    )
                                    logger.info(
                                        "Queued news impact for AssetID %s, NewsID %s, Amount: %s to Redis key %s.",
                                        target_asset_id,
                                        market_news_id,
                                        eff_impact_amount,
                                        redis_key,
                                    )
                                else:
                                    logger.error(
                                        "Redis client not available to queue news impact."
                                    )
                            except Exception as e_redis:
                                logger.error(
                                    "Failed to queue news impact to Redis for AssetID %s, NewsID %s: %s",
                                    target_asset_id,
                                    market_news_id,
                                    e_redis,
                                    exc_info=True,
                                )
                        if selected_stock_details["linked_criteria_type"]:
                            cat_keys = [
                                ":".join(
                                    filter(
                                        None,
                                        [
                                            selected_stock_details[
                                                "linked_criteria_type"
                                            ],
                                            selected_stock_details[
                                                "linked_criteria_value"
                                            ],
                                            selected_stock_details[
                                                "linked_pool_context"
                                            ],
                                        ],
                                    )
                                )
                            ]
                            temp_offset = Decimal("0.0")
                            if chosen_sentiment == "positive":
                                temp_offset = Decimal(
                                    str(temp_effect_config.offset_positive)
                                )
                            elif chosen_sentiment == "negative":
                                temp_offset = Decimal(
                                    str(temp_effect_config.offset_negative)
                                )
                            if temp_offset != Decimal("0.0"):
                                temp_offset *= dampening_factor
                                expiry = datetime.now(timezone.utc) + timedelta(
                                    hours=random.randint(
                                        temp_effect_config.duration_hours_min,
                                        temp_effect_config.duration_hours_max,
                                    )
                                )
                                final_mod = Decimal("1.0") + temp_offset
                                for key in cat_keys:
                                    await conn.execute(
                                        "INSERT INTO gacha_category_stock_influence (category_key, temporary_news_modifier, news_effect_expiry) VALUES ($1, $2, $3) ON CONFLICT (category_key) DO UPDATE SET temporary_news_modifier = EXCLUDED.temporary_news_modifier, news_effect_expiry = EXCLUDED.news_effect_expiry",
                                        key,
                                        final_mod,
                                        expiry,
                                    )
                                    logger.info(
                                        "Gacha category '%s' temp mod updated to %s.",
                                        key,
                                        final_mod,
                                    )
        except Exception as e:
            logger.error(
                "Error in _generate_specific_news_type for '%s': %s",
                news_type_to_generate,
                e,
                exc_info=True,
            )
    logger.info("Finished _generate_specific_news_type for: %s", news_type_to_generate)


async def _calculate_market_aggregates(conn: asyncpg.Connection) -> Dict[str, Decimal]:
    """計算整體市場的聚合數據"""
    try:
        query = """
            SELECT
                current_price,
                base_volatility,
                volatility_factor,
                total_shares
            FROM virtual_assets
            WHERE current_price > 0
                AND lifecycle_status = 'active'
                AND (initial_anchor_price IS NOT NULL OR current_anchor_price IS NOT NULL)
        """
        stocks = await conn.fetch(query)

        if not stocks:
            return {
                "avg_price": Decimal("100.0"),
                "avg_base_volatility": Decimal("0.015"),
                "avg_volatility_factor": Decimal("1.25"),
                "total_market_cap": Decimal("0"),
                "active_stocks_count": Decimal("0"),
            }

        total_market_cap = Decimal("0")
        weighted_price_sum = Decimal("0")
        volatility_sum = Decimal("0")
        volatility_factor_sum = Decimal("0")

        for stock in stocks:
            price = Decimal(str(stock["current_price"]))
            shares = Decimal(str(stock["total_shares"]))
            market_cap = price * shares

            total_market_cap += market_cap
            weighted_price_sum += price * market_cap
            volatility_sum += Decimal(str(stock["base_volatility"]))
            volatility_factor_sum += Decimal(str(stock["volatility_factor"]))

        stock_count = len(stocks)
        avg_price = (
            weighted_price_sum / total_market_cap
            if total_market_cap > 0
            else Decimal("100.0")
        )
        avg_base_volatility = volatility_sum / stock_count
        avg_volatility_factor = volatility_factor_sum / stock_count

        return {
            "avg_price": avg_price,
            "avg_base_volatility": avg_base_volatility,
            "avg_volatility_factor": avg_volatility_factor,
            "total_market_cap": total_market_cap,
            "active_stocks_count": Decimal(stock_count),
        }

    except Exception as e:
        logger.error("Error calculating market aggregates: %s", e, exc_info=True)
        return {
            "avg_price": Decimal("100.0"),
            "avg_base_volatility": Decimal("0.015"),
            "avg_volatility_factor": Decimal("1.25"),
            "total_market_cap": Decimal("0"),
            "active_stocks_count": Decimal("0"),
        }


async def _generate_market_overview(
    conn: asyncpg.Connection, selected_stock_details: Optional[Dict]
) -> str:
    """生成市場概況描述"""
    try:
        if selected_stock_details:
            return "市場整體呈現震盪格局，個股表現分化。"

        market_aggregates = await _calculate_market_aggregates(conn)
        active_count = market_aggregates["active_stocks_count"]
        total_market_cap = market_aggregates["total_market_cap"]

        if active_count == 0:
            return "市場整體呈現停滯狀態，缺乏活躍交易標的。"
        elif active_count < 5:
            return f"市場規模較小，僅有{active_count}檔活躍股票，總市值約{total_market_cap / 10000:.1f}萬油幣。"
        elif total_market_cap < 1000000:
            return f"市場處於發展階段，{active_count}檔活躍股票總市值約{total_market_cap / 10000:.1f}萬油幣，投資者情緒謹慎。"
        else:
            return f"市場活躍度良好，{active_count}檔股票總市值達{total_market_cap / 10000:.1f}萬油幣，整體呈現震盪格局。"

    except Exception as e:
        logger.error("Error generating market overview: %s", e, exc_info=True)
        return "市場整體呈現震盪格局。"


async def _generate_player_activity_summary(conn: asyncpg.Connection) -> str:
    """生成玩家活動摘要"""
    try:
        query = """
            SELECT COUNT(*) as transaction_count,
                   COUNT(DISTINCT user_id) as active_users,
                   SUM(CASE WHEN transaction_type = 'BUY' THEN quantity ELSE 0 END) as total_buy_volume,
                   SUM(CASE WHEN transaction_type = 'SELL' THEN quantity ELSE 0 END) as total_sell_volume
            FROM market_transactions
            WHERE timestamp >= NOW() - INTERVAL '24 hours'
        """
        result = await conn.fetchrow(query)

        if not result or result["transaction_count"] == 0:
            return "市場交易清淡，投資者觀望情緒濃厚。"

        transaction_count = result["transaction_count"]
        active_users = result["active_users"]
        buy_volume = result["total_buy_volume"] or 0
        sell_volume = result["total_sell_volume"] or 0

        if transaction_count < 10:
            return "少數投資者有零星交易動作。"
        elif active_users < 5:
            return "部分大戶有調倉動作。"
        elif buy_volume > sell_volume * 1.5:
            return f"{active_users}位投資者參與交易，買盤明顯強於賣盤，市場情緒偏多。"
        elif sell_volume > buy_volume * 1.5:
            return f"{active_users}位投資者參與交易，賣壓較重，獲利了結情緒明顯。"
        else:
            return f"{active_users}位投資者參與交易，買賣力道相當，市場呈現均衡狀態。"

    except Exception as e:
        logger.error("Error generating player activity summary: %s", e, exc_info=True)
        return "部分大戶有調倉動作。"


async def _create_and_store_lifecycle_news(
    news_type: str,
    asset_id: int,
    asset_name: str,
    asset_symbol: str,
    context_data: Dict[str, Any],
    custom_headline: Optional[str] = None,
) -> Optional[Dict[str, Any]]:
    logger.info(
        "Attempting to create lifecycle news: Type='%s', Asset='%s (%s)', AssetID='%s'",
        news_type,
        asset_name,
        asset_symbol,
        asset_id,
    )
    type_def = news_type_definitions.get(news_type)
    if not type_def:
        logger.error("Lifecycle news type definition for '%s' not found.", news_type)
        return None
    prompt_template_key = type_def.get("prompt_template_key")
    if not prompt_template_key or not isinstance(prompt_template_key, str):
        logger.error(
            "Prompt template key not defined or not a string for lifecycle news type '%s'.",
            news_type,
        )
        return None
    context_data.setdefault("asset_name", asset_name)
    context_data.setdefault("asset_symbol", asset_symbol)
    archetypes = type_def.get("archetypes")
    if not archetypes or not isinstance(archetypes, list):
        archetypes = ["SYSTEM_ANNOUNCER"]
    chosen_character_archetype = random.choice(archetypes)
    raw_ai_text = ""
    try:
        formatted_prompts = prompt_service.get_formatted_prompt(
            prompt_template_key,
            chosen_character_archetype,
            context_data,
        )
        if (
            not formatted_prompts
            or not formatted_prompts[0]
            or (not formatted_prompts[1])
        ):
            logger.error(
                "Failed to get formatted prompts for news_type='%s', archetype='%s'.",
                prompt_template_key,
                chosen_character_archetype,
            )
            return None
        system_prompt_text = formatted_prompts[0]
        user_prompt_text = formatted_prompts[1]
        api_response_obj = await ai_service.process_text(
            prompt=user_prompt_text, system_prompt=system_prompt_text
        )
        raw_ai_text = ai_service.extract_response_text(api_response_obj)
        parsed_data = _parse_ai_news_response(raw_ai_text, news_type)
        headline = parsed_data["headline"]
        content = parsed_data["content"]
        if custom_headline and headline == "AI生成新聞標題失敗":
            headline = custom_headline[:250]
            parsed_data["headline"] = headline
            logger.info(
                "Using custom headline for %s on %s due to AI failure: '%s'",
                news_type,
                asset_name,
                headline,
            )
        if headline == "AI生成新聞標題失敗" or content == "AI生成新聞內容失敗":
            logger.error(
                "Failed to generate or parse lifecycle news '%s' for asset %s (%s). AI Response: %s. Parsed: %s",
                news_type,
                asset_id,
                asset_name,
                raw_ai_text[:500],
                parsed_data,
            )
        raw_data_input_for_db: Dict[str, Any] = {}
        raw_data_input_for_db.update(context_data)
        raw_data_input_for_db["_ai_parsed_response_"] = parsed_data
        char_name = get_character_archetype_readable_names().get(
            chosen_character_archetype, chosen_character_archetype
        )
        impacts_price = type_def.get("impacts_price", False)
        lifecycle_sentiment = "NEUTRAL"
        news_id: Optional[int] = None
        pool = get_pool()
        if pool is None:
            logger.error("Database pool is not initialized.")
            return None
        async with pool.acquire() as conn:
            async with conn.transaction():
                news_id = await conn.fetchval(
                    "INSERT INTO market_news\n                         (headline, content, affected_asset_id, sentiment, source, news_type,\n                          character_archetype, character_name, impacts_price, raw_data_input)\n                         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)\n                         RETURNING id",
                    headline,
                    content,
                    asset_id,
                    lifecycle_sentiment,
                    "SYSTEM_LIFECYCLE_EVENT",
                    news_type,
                    chosen_character_archetype,
                    char_name,
                    impacts_price,
                    raw_data_input_for_db,
                )
        if news_id:
            logger.info(
                "Successfully stored lifecycle news ID %s: Type='%s', AssetID='%s', Headline='%s'",
                news_id,
                news_type,
                asset_id,
                headline,
            )
            return {
                "id": news_id,
                "headline": headline,
                "content": content,
                "parsed_data": parsed_data,
            }
        else:
            logger.error(
                "Failed to store lifecycle news for %s on asset %s after parsing.",
                news_type,
                asset_id,
            )
            return None
    except Exception as e:
        logger.error(
            "Exception in _create_and_store_lifecycle_news for '%s' on asset %s: %s. AI Response: %s",
            news_type,
            asset_id,
            e,
            raw_ai_text[:500],
            exc_info=True,
        )
        return None


async def generate_st_warning_news(
    asset_id: int,
    asset_name: str,
    asset_symbol: str,
    current_price: Decimal,
    reason: str = "觸發ST條件",
) -> Optional[Dict[str, Any]]:
    news_type = "STOCK_ST_WARNING"
    context_data = {"current_price": str(current_price), "reason": reason}
    custom_headline = f"警示：{asset_name} ({asset_symbol}) 被標記為ST股票"
    return await _create_and_store_lifecycle_news(
        news_type=news_type,
        asset_id=asset_id,
        asset_name=asset_name,
        asset_symbol=asset_symbol,
        context_data=context_data,
        custom_headline=custom_headline,
    )


async def generate_st_recovery_news(
    asset_id: int, asset_name: str, asset_symbol: str, current_price: Decimal
) -> Optional[Dict[str, Any]]:
    news_type = "STOCK_ST_RECOVERY"
    context_data = {"current_price": str(current_price)}
    custom_headline = f"恢復：{asset_name} ({asset_symbol}) 解除ST標記"
    return await _create_and_store_lifecycle_news(
        news_type=news_type,
        asset_id=asset_id,
        asset_name=asset_name,
        asset_symbol=asset_symbol,
        context_data=context_data,
        custom_headline=custom_headline,
    )


async def generate_delist_news(
    asset_id: int,
    asset_name: str,
    asset_symbol: str,
    final_buyback_price: Optional[Decimal],
    reason: str = "符合退市條件",
) -> Optional[Dict[str, Any]]:
    news_type = "STOCK_DELISTED"
    context_data = {
        "final_buyback_price": (
            str(final_buyback_price) if final_buyback_price is not None else "N/A"
        ),
        "reason": reason,
    }
    custom_headline = f"公告：{asset_name} ({asset_symbol}) 確認退市"
    return await _create_and_store_lifecycle_news(
        news_type=news_type,
        asset_id=asset_id,
        asset_name=asset_name,
        asset_symbol=asset_symbol,
        context_data=context_data,
        custom_headline=custom_headline,
    )


async def generate_new_listing_news(
    asset_id: int,
    asset_name: str,
    asset_symbol: str,
    initial_price: Decimal,
    total_shares: int,
    **kwargs,
) -> Optional[Dict[str, Any]]:
    news_type = "STOCK_NEW_LISTING"
    context_data = {
        "initial_price": str(initial_price),
        "total_shares": str(total_shares),
    }
    if "linked_criteria_type" in kwargs:
        context_data["linked_criteria_type"] = kwargs["linked_criteria_type"]
    if "linked_criteria_value" in kwargs:
        context_data["linked_criteria_value"] = kwargs["linked_criteria_value"]
    context_data["linked_pool_context"] = kwargs.get("linked_pool_context") or ""
    if "linked_criteria_description" in kwargs:
        context_data["linked_criteria_description"] = kwargs[
            "linked_criteria_description"
        ]
    else:
        logger.warning(
            "generate_new_listing_news called for %s without 'linked_criteria_description' in kwargs. Using default.",
            asset_symbol,
        )
        context_data["linked_criteria_description"] = "綜合市場型"
    custom_headline = f"上市：新股 {asset_name} ({asset_symbol}) 開始交易"
    return await _create_and_store_lifecycle_news(
        news_type=news_type,
        asset_id=asset_id,
        asset_name=asset_name,
        asset_symbol=asset_symbol,
        context_data=context_data,
        custom_headline=custom_headline,
    )
