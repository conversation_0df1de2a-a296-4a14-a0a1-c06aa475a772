from __future__ import annotations

import random
from collections import defaultdict
from typing import Dict, List

from database.postgresql.async_manager import get_redis_client
from gacha.constants import <PERSON><PERSON><PERSON>evel
from gacha.repositories._base_repo import fetch_all
from utils.logger import logger

# --- Redis緩存配置 ---
CARD_POOL_CACHE_KEY = "gacha:card_pool_cache"
CARD_POOL_CACHE_TTL = 3600  # 1小時


async def get_indexed_master_cache() -> Dict[str, Dict[RarityLevel, List[int]]]:
    """獲取預先按 pool_type 和 rarity 索引好的卡片ID緩存。"""
    redis_client = get_redis_client()

    # 優先從Redis獲取緩存
    if redis_client:
        try:
            import json

            cached_raw = await redis_client.execute_command(
                "JSON.GET", CARD_POOL_CACHE_KEY
            )
            cached_data = json.loads(cached_raw) if cached_raw else None
            if cached_data:
                # 將字符串鍵轉換回RarityLevel枚舉
                converted_cache = {}
                for pool_type, rarity_dict in cached_data.items():
                    converted_cache[pool_type] = {
                        RarityLevel(int(rarity_key)): card_list
                        for rarity_key, card_list in rarity_dict.items()
                    }
                logger.debug("[CACHE] 從Redis獲取卡池緩存成功")
                return converted_cache
        except Exception as e:
            logger.warning("[CACHE] 從Redis獲取卡池緩存失敗: %s", e)

    # 從數據庫重建緩存
    return await _populate_and_index_master_cache()


async def _populate_and_index_master_cache() -> Dict[str, Dict[RarityLevel, List[int]]]:
    """從資料庫獲取所有卡片，並直接構建成一個嵌套的、索引好的字典。"""
    logger.info("[CACHE] 開始構建索引化卡片主資料快取...")

    query = "SELECT card_id, pool_type, rarity FROM gacha_master_cards"
    try:
        all_cards_from_db = await fetch_all(query)
        all_cards_dicts = [dict(record) for record in all_cards_from_db]

        temp_cache = defaultdict(lambda: defaultdict(list))

        for card_record in all_cards_dicts:
            try:
                pool_type = card_record.get("pool_type")
                card_id = card_record.get("card_id")
                rarity_val = card_record.get("rarity")

                if not all((pool_type, card_id, rarity_val is not None)):
                    continue

                rarity_enum = RarityLevel(rarity_val)
                temp_cache[pool_type][rarity_enum].append(card_id)

            except (KeyError, ValueError, TypeError) as e:
                logger.warning(
                    f"[CACHE] 處理卡片記錄時跳過一筆無效數據: {card_record}, 錯誤: {e}"
                )
                continue

        # 對每個池子裡的每個稀有度的卡片列表進行隨機化
        for pool in temp_cache.values():
            for rarity_list in pool.values():
                random.shuffle(rarity_list)

        indexed_cache = dict(temp_cache)
        total_cards = len(all_cards_from_db)

        # 存儲到Redis (將RarityLevel枚舉轉為字符串以支持JSON序列化)
        redis_client = get_redis_client()
        if redis_client:
            try:
                serializable_cache = {
                    pool_type: {
                        str(rarity.value): card_list
                        for rarity, card_list in rarity_dict.items()
                    }
                    for pool_type, rarity_dict in indexed_cache.items()
                }
                import json

                await redis_client.execute_command(
                    "JSON.SET", CARD_POOL_CACHE_KEY, "$", json.dumps(serializable_cache)
                )
                await redis_client.expire(CARD_POOL_CACHE_KEY, CARD_POOL_CACHE_TTL)
                logger.info("[CACHE] 卡池緩存已同步到Redis")
            except Exception as e:
                logger.warning("[CACHE] 卡池緩存同步到Redis失敗: %s", e)

        logger.info(
            f"✅ [CACHE] 索引化卡片主資料快取構建完成，共處理 {total_cards} 張卡片。"
        )
        return dict(indexed_cache)

    except Exception as e:
        logger.error("❌ [CACHE] 構建索引化卡片主資料快取失敗: %s", e, exc_info=True)
        return {}
